export type CatStatus = "available" | "pending" | "adopted" | "unavailable";

export type CatSummary = {
	id: string;
	name: string;
	slug: string;
	age: number; // Changed from string to number
	gender: string;
	breed: string;
	breedId: number;
	location: string;
	wilayaId: number;
	communeId: number;
	imageUrl: string;
	vaccinated?: boolean | null;
	neutered?: boolean | null;
	specialNeeds?: boolean | null;
	adopted?: boolean | null;
	isDraft?: boolean;
	status?: CatStatus;
	createdAt?: string | Date;
	description: string;
};

export type CatDetail = CatSummary & {
	story?: string | null;
	specialNeedsDescription?: string | null;
	breedName?: string;
	images?: {
		id?: number;
		url: string;
		isPrimary: boolean | null;
		catId?: number;
		createdAt?: Date;
	}[];
	userId?: number;
	user?: {
		id: number;
		slug: string;
		name: string;
		email: string;
		image?: string | null;
	};
};

export type CatWithPagination = {
	cats: CatSummary[];
	pagination: {
		total: number;
		pageCount?: number; // Optional for cursor-based pagination
		page?: number; // Optional for cursor-based pagination
		limit: number;
		// Cursor-based pagination fields
		hasNextPage?: boolean;
		nextCursor?: string;
		useCursor?: boolean;
	};
};
