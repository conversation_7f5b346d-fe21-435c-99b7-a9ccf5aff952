#!/usr/bin/env bun

/**
 * <PERSON><PERSON><PERSON> to analyze current pagination performance issues
 * This script will:
 * 1. Check current database size (number of cats)
 * 2. Test pagination performance at different page numbers
 * 3. Measure query execution times for offset-based pagination
 * 4. Document performance degradation with large offsets
 */

import { db } from "@/lib/db";
import { cats } from "@/lib/db/schema";
import { count, desc, eq } from "drizzle-orm";

interface PaginationTest {
	page: number;
	limit: number;
	offset: number;
	queryTime: number;
	countTime: number;
	totalTime: number;
}

async function analyzePaginationPerformance() {
	console.log("🔍 Analyzing Pagination Performance Issues\n");
	
	// 1. Get current database size
	console.log("📊 Database Statistics:");
	const startTime = performance.now();
	
	const [totalCatsResult] = await db
		.select({ value: count() })
		.from(cats);
	
	const [publishedCatsResult] = await db
		.select({ value: count() })
		.from(cats)
		.where(eq(cats.isDraft, false));
	
	const countTime = performance.now() - startTime;
	
	console.log(`   Total cats: ${totalCatsResult.value}`);
	console.log(`   Published cats: ${publishedCatsResult.value}`);
	console.log(`   Count query time: ${countTime.toFixed(2)}ms\n`);
	
	// 2. Test pagination performance at different page numbers
	console.log("⚡ Pagination Performance Tests:");
	console.log("   Testing offset-based pagination at different page numbers...\n");
	
	const limit = 12; // Standard page size
	const testPages = [1, 5, 10, 25, 50, 100, 200]; // Test various page numbers
	const results: PaginationTest[] = [];
	
	for (const page of testPages) {
		const offset = (page - 1) * limit;
		
		// Skip if offset exceeds available data
		if (offset >= publishedCatsResult.value) {
			console.log(`   ⏭️  Skipping page ${page} (offset ${offset} exceeds data)`);
			continue;
		}
		
		console.log(`   📄 Testing page ${page} (offset: ${offset})...`);
		
		// Test data query performance
		const dataStartTime = performance.now();
		const catsData = await db.query.cats.findMany({
			where: eq(cats.isDraft, false),
			with: {
				images: true,
				breed: true,
				wilaya: true,
				commune: true,
			},
			orderBy: desc(cats.createdAt),
			limit,
			offset,
		});
		const dataQueryTime = performance.now() - dataStartTime;
		
		// Test count query performance (simulating real pagination)
		const countStartTime = performance.now();
		const [countResult] = await db
			.select({ value: count() })
			.from(cats)
			.where(eq(cats.isDraft, false));
		const countQueryTime = performance.now() - countStartTime;
		
		const totalTime = dataQueryTime + countQueryTime;
		
		const result: PaginationTest = {
			page,
			limit,
			offset,
			queryTime: dataQueryTime,
			countTime: countQueryTime,
			totalTime,
		};
		
		results.push(result);
		
		console.log(`      Data query: ${dataQueryTime.toFixed(2)}ms`);
		console.log(`      Count query: ${countQueryTime.toFixed(2)}ms`);
		console.log(`      Total time: ${totalTime.toFixed(2)}ms`);
		console.log(`      Results returned: ${catsData.length}\n`);
	}
	
	// 3. Analyze performance degradation
	console.log("📈 Performance Analysis:");
	
	if (results.length > 1) {
		const firstPageTime = results[0].totalTime;
		const lastPageTime = results[results.length - 1].totalTime;
		const degradation = ((lastPageTime - firstPageTime) / firstPageTime) * 100;
		
		console.log(`   First page (${results[0].page}) total time: ${firstPageTime.toFixed(2)}ms`);
		console.log(`   Last page (${results[results.length - 1].page}) total time: ${lastPageTime.toFixed(2)}ms`);
		console.log(`   Performance degradation: ${degradation.toFixed(1)}%\n`);
		
		// Find slowest queries
		const slowestDataQuery = results.reduce((prev, current) => 
			prev.queryTime > current.queryTime ? prev : current
		);
		const slowestCountQuery = results.reduce((prev, current) => 
			prev.countTime > current.countTime ? prev : current
		);
		
		console.log(`   Slowest data query: Page ${slowestDataQuery.page} (${slowestDataQuery.queryTime.toFixed(2)}ms)`);
		console.log(`   Slowest count query: Page ${slowestCountQuery.page} (${slowestCountQuery.countTime.toFixed(2)}ms)\n`);
	}
	
	// 4. Document issues with current approach
	console.log("🚨 Identified Issues with Current Offset-Based Pagination:");
	console.log("   1. Performance degrades linearly with page number");
	console.log("   2. Large offsets require database to skip many rows");
	console.log("   3. Count queries are executed on every page load");
	console.log("   4. No caching mechanism for total counts");
	console.log("   5. Inconsistent results if data changes during pagination");
	console.log("   6. Memory usage increases with larger offsets\n");
	
	// 5. Recommendations
	console.log("💡 Recommended Optimizations:");
	console.log("   1. Implement cursor-based pagination using createdAt + id");
	console.log("   2. Cache total count results with appropriate TTL");
	console.log("   3. Use approximate counts for large datasets");
	console.log("   4. Consider virtual scrolling for better UX");
	console.log("   5. Implement pagination result caching");
	console.log("   6. Add database connection pooling optimization\n");
	
	return {
		totalCats: totalCatsResult.value,
		publishedCats: publishedCatsResult.value,
		testResults: results,
		countQueryTime: countTime,
	};
}

// Run the analysis
analyzePaginationPerformance()
	.then((results) => {
		console.log("✅ Pagination performance analysis completed!");
		console.log(`📊 Tested ${results.testResults.length} different page numbers`);
		process.exit(0);
	})
	.catch((error) => {
		console.error("❌ Error analyzing pagination performance:", error);
		process.exit(1);
	});
