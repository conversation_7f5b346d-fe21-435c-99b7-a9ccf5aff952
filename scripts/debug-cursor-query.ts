#!/usr/bin/env bun

/**
 * Debug script for cursor-based pagination
 */

import { db } from "@/lib/db";
import { cats } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import {
	buildCatFilters,
	getSortOrder,
	analyzeJoinRequirements,
	getCursorBasedCatQuery,
	getOptimizedCatQuery,
	transformCatResultsWithImages,
} from "@/lib/trpc/routers/helpers/cat-helpers";

async function debugCursorQuery() {
	console.log("🔍 Debugging Cursor-Based Query\n");
	
	const limit = 12;
	const testInput = {
		limit,
		sort: "newest" as const,
	};
	
	const baseConditions = buildCatFilters(testInput);
	const sortOrder = getSortOrder(testInput.sort);
	const joinRequirements = analyzeJoinRequirements(testInput, false);
	
	console.log("📊 Base conditions:", baseConditions.length);
	console.log("📊 Join requirements:", joinRequirements);
	console.log("📊 Sort order:", testInput.sort);
	
	// Test offset-based query first
	console.log("\n🔍 Testing offset-based query:");
	const offsetResults = await getOptimizedCatQuery(
		db,
		baseConditions,
		sortOrder,
		limit,
		0,
		{ ...joinRequirements, needsImages: true }
	);
	
	console.log(`   Raw results count: ${offsetResults.length}`);
	console.log(`   First result structure:`, Object.keys(offsetResults[0] || {}));
	
	if (offsetResults.length > 0) {
		console.log(`   First cat ID: ${offsetResults[0].cats?.id}`);
		console.log(`   First cat name: ${offsetResults[0].cats?.name}`);
		console.log(`   First cat createdAt: ${offsetResults[0].cats?.createdAt}`);
	}
	
	const transformedOffset = transformCatResultsWithImages(offsetResults, limit);
	console.log(`   Transformed results count: ${transformedOffset.length}`);
	
	// Test cursor-based query
	console.log("\n🎯 Testing cursor-based query:");
	const cursorResults = await getCursorBasedCatQuery(
		db,
		baseConditions,
		sortOrder,
		limit,
		undefined, // No cursor for first page
		{ ...joinRequirements, needsImages: true },
		undefined,
		testInput.sort
	);
	
	console.log(`   Raw results count: ${cursorResults.length}`);
	console.log(`   First result structure:`, Object.keys(cursorResults[0] || {}));
	
	if (cursorResults.length > 0) {
		console.log(`   First cat ID: ${cursorResults[0].cats?.id}`);
		console.log(`   First cat name: ${cursorResults[0].cats?.name}`);
		console.log(`   First cat createdAt: ${cursorResults[0].cats?.createdAt}`);
	}
	
	const transformedCursor = transformCatResultsWithImages(cursorResults, limit);
	console.log(`   Transformed results count: ${transformedCursor.length}`);
	
	// Compare first few IDs
	if (transformedOffset.length > 0 && transformedCursor.length > 0) {
		console.log("\n🔍 Comparing results:");
		console.log("   Offset IDs:", transformedOffset.slice(0, 3).map(r => r.cats?.id));
		console.log("   Cursor IDs:", transformedCursor.slice(0, 3).map(r => r.cats?.id));
	}
	
	// Test with a simple direct query to see what we should expect
	console.log("\n🔍 Testing direct query:");
	const directResults = await db.query.cats.findMany({
		where: eq(cats.isDraft, false),
		orderBy: sortOrder,
		limit,
		with: {
			images: true,
			breed: true,
			wilaya: true,
			commune: true,
		},
	});
	
	console.log(`   Direct results count: ${directResults.length}`);
	if (directResults.length > 0) {
		console.log(`   First direct cat ID: ${directResults[0].id}`);
		console.log(`   First direct cat name: ${directResults[0].name}`);
		console.log(`   First direct cat createdAt: ${directResults[0].createdAt}`);
	}
}

// Run the debug
debugCursorQuery()
	.then(() => {
		console.log("\n✅ Debug completed!");
		process.exit(0);
	})
	.catch((error) => {
		console.error("❌ Error debugging cursor query:", error);
		process.exit(1);
	});
