#!/usr/bin/env bun

/**
 * <PERSON><PERSON>t to test cursor-based pagination performance
 * This script will:
 * 1. Test cursor-based pagination performance
 * 2. Compare with offset-based pagination
 * 3. Measure performance improvements
 * 4. Test pagination consistency
 */

import { db } from "@/lib/db";
import { cats } from "@/lib/db/schema";
import { count, desc, eq } from "drizzle-orm";
import {
	buildCatFilters,
	getSortOrder,
	analyzeJoinRequirements,
	getCursorBasedCatQuery,
	getOptimizedCatQuery,
	transformCatResultsWithImages,
	encodeCursor,
	decodeCursor,
} from "@/lib/trpc/routers/helpers/cat-helpers";

interface PaginationTestResult {
	method: "offset" | "cursor";
	page: number;
	queryTime: number;
	resultsCount: number;
	cursor?: string;
}

async function testCursorPagination() {
	console.log("🔍 Testing Cursor-Based Pagination Performance\n");

	// Get total count
	const [totalResult] = await db
		.select({ value: count() })
		.from(cats)
		.where(eq(cats.isDraft, false));

	console.log(`📊 Total published cats: ${totalResult.value}\n`);

	const limit = 12;
	const testPages = 5; // Test first 5 pages

	// Test input (simulating typical query)
	const testInput = {
		limit,
		sort: "newest" as const,
	};

	const baseConditions = buildCatFilters(testInput);
	const sortOrder = getSortOrder(testInput.sort);
	const joinRequirements = analyzeJoinRequirements(testInput, false);

	console.log("⚡ Performance Comparison: Offset vs Cursor Pagination\n");

	const offsetResults: PaginationTestResult[] = [];
	const cursorResults: PaginationTestResult[] = [];

	// Test offset-based pagination
	console.log("📄 Testing Offset-Based Pagination:");
	for (let page = 1; page <= testPages; page++) {
		const offset = (page - 1) * limit;

		const startTime = performance.now();
		const results = await getOptimizedCatQuery(
			db,
			baseConditions,
			sortOrder,
			limit,
			offset,
			{ ...joinRequirements, needsImages: true }
		);
		const queryTime = performance.now() - startTime;

		const transformedResults = transformCatResultsWithImages(
			results,
			limit
		);

		offsetResults.push({
			method: "offset",
			page,
			queryTime,
			resultsCount: transformedResults.length,
		});

		console.log(
			`   Page ${page}: ${queryTime.toFixed(2)}ms (${transformedResults.length} results)`
		);
	}

	// Test cursor-based pagination
	console.log("\n🎯 Testing Cursor-Based Pagination:");
	let currentCursor: string | undefined;

	for (let page = 1; page <= testPages; page++) {
		const startTime = performance.now();
		const results = await getCursorBasedCatQuery(
			db,
			baseConditions,
			sortOrder,
			limit,
			currentCursor,
			{ ...joinRequirements, needsImages: true },
			undefined,
			testInput.sort
		);
		const queryTime = performance.now() - startTime;

		// Check if there are more results and generate next cursor
		const hasMore = results.length > limit;
		const actualResults = hasMore ? results.slice(0, limit) : results;

		if (hasMore && actualResults.length > 0) {
			const lastItem = actualResults[actualResults.length - 1];
			currentCursor = encodeCursor({
				createdAt: lastItem.cats.createdAt,
				id: lastItem.cats.id,
			});
		} else {
			currentCursor = undefined;
		}

		const transformedResults = transformCatResultsWithImages(
			actualResults,
			limit
		);

		cursorResults.push({
			method: "cursor",
			page,
			queryTime,
			resultsCount: transformedResults.length,
			cursor: currentCursor,
		});

		console.log(
			`   Page ${page}: ${queryTime.toFixed(2)}ms (${transformedResults.length} results)`
		);

		// Stop if no more results
		if (!hasMore) {
			console.log(`   No more results after page ${page}`);
			break;
		}
	}

	// Performance analysis
	console.log("\n📈 Performance Analysis:");

	const avgOffsetTime =
		offsetResults.reduce((sum, r) => sum + r.queryTime, 0) /
		offsetResults.length;
	const avgCursorTime =
		cursorResults.reduce((sum, r) => sum + r.queryTime, 0) /
		cursorResults.length;
	const improvement = ((avgOffsetTime - avgCursorTime) / avgOffsetTime) * 100;

	console.log(`   Average offset time: ${avgOffsetTime.toFixed(2)}ms`);
	console.log(`   Average cursor time: ${avgCursorTime.toFixed(2)}ms`);
	console.log(`   Performance improvement: ${improvement.toFixed(1)}%`);

	// Test consistency
	console.log("\n🔍 Testing Pagination Consistency:");

	// Get first page with both methods and compare
	const offsetFirstPage = await getOptimizedCatQuery(
		db,
		baseConditions,
		sortOrder,
		limit,
		0,
		{ ...joinRequirements, needsImages: true }
	);

	const cursorFirstPage = await getCursorBasedCatQuery(
		db,
		baseConditions,
		sortOrder,
		limit,
		undefined,
		{ ...joinRequirements, needsImages: true },
		undefined,
		testInput.sort
	);

	const offsetIds = transformCatResultsWithImages(offsetFirstPage, limit).map(
		(cat) => cat.cats.id
	);
	const cursorIds = transformCatResultsWithImages(
		cursorFirstPage.slice(0, limit),
		limit
	).map((cat) => cat.cats.id);

	const idsMatch = JSON.stringify(offsetIds) === JSON.stringify(cursorIds);
	console.log(
		`   First page results match: ${idsMatch ? "✅ Yes" : "❌ No"}`
	);

	if (!idsMatch) {
		console.log(`   Offset IDs: ${offsetIds.slice(0, 3).join(", ")}...`);
		console.log(`   Cursor IDs: ${cursorIds.slice(0, 3).join(", ")}...`);
	}

	// Test cursor encoding/decoding
	console.log("\n🔧 Testing Cursor Encoding/Decoding:");

	if (offsetFirstPage.length > 0) {
		const testItem = offsetFirstPage[0];
		const originalCursor = {
			createdAt: testItem.cats.createdAt,
			id: testItem.cats.id,
		};

		const encoded = encodeCursor(originalCursor);
		const decoded = decodeCursor(encoded);

		const encodingWorks =
			decoded &&
			decoded.id === originalCursor.id &&
			decoded.createdAt.getTime() === originalCursor.createdAt.getTime();

		console.log(
			`   Cursor encoding/decoding: ${encodingWorks ? "✅ Working" : "❌ Failed"}`
		);
		console.log(`   Sample cursor: ${encoded.substring(0, 20)}...`);
	}

	return {
		offsetResults,
		cursorResults,
		avgOffsetTime,
		avgCursorTime,
		improvement,
		consistencyCheck: idsMatch,
	};
}

// Run the test
testCursorPagination()
	.then((results) => {
		console.log("\n✅ Cursor pagination testing completed!");
		console.log(
			`🚀 Performance improvement: ${results.improvement.toFixed(1)}%`
		);
		console.log(
			`🔍 Consistency check: ${results.consistencyCheck ? "Passed" : "Failed"}`
		);
		process.exit(0);
	})
	.catch((error) => {
		console.error("❌ Error testing cursor pagination:", error);
		process.exit(1);
	});
