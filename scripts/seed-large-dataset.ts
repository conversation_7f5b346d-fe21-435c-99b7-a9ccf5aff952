#!/usr/bin/env bun

/**
 * <PERSON><PERSON><PERSON> to seed a large dataset for pagination performance testing
 * This will create thousands of cat records to properly test pagination performance
 */

import { db } from "@/lib/db";
import {
	cats,
	catImages,
	users,
	catBreeds,
	wilayas,
	communes,
} from "@/lib/db/schema";
import { eq, count } from "drizzle-orm";
import { generateUniqueCatSlug } from "@/lib/utils/slug";

const CAT_NAMES = [
	"Whiskers",
	"<PERSON>",
	"Luna",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"Angel",
	"<PERSON><PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>",
	"<PERSON>",
];

const CAT_DESCRIPTIONS = [
	"A friendly and playful cat looking for a loving home.",
	"Very affectionate and loves to cuddle with humans.",
	"Independent but social, gets along well with other cats.",
	"Energetic kitten who loves to play with toys.",
	"Calm and gentle, perfect for a quiet household.",
	"Loves to explore and is very curious about everything.",
	"<PERSON> and loving, enjoys being petted and pampered.",
	"Playful and mischievous, always getting into fun trouble.",
	"Quiet and reserved but very loyal once trust is earned.",
	"Outgoing and social, loves meeting new people.",
];

async function seedLargeDataset(targetCount: number = 1000) {
	console.log(
		`🌱 Seeding large dataset with ${targetCount} cats for pagination testing...\n`
	);

	// Check current count
	const [currentCountResult] = await db.select({ value: count() }).from(cats);

	console.log(`📊 Current cats in database: ${currentCountResult.value}`);

	if (currentCountResult.value >= targetCount) {
		console.log(
			`✅ Database already has ${currentCountResult.value} cats, which is >= ${targetCount}`
		);
		return;
	}

	// Get reference data for foreign keys
	console.log("🔍 Fetching reference data...");

	const existingUsers = await db.query.users.findMany({
		columns: { id: true },
	});

	const existingBreeds = await db.query.catBreeds.findMany({
		columns: { id: true },
	});

	const existingWilayas = await db.query.wilayas.findMany({
		columns: { id: true },
	});

	const existingCommunes = await db.query.communes.findMany({
		columns: { id: true },
	});

	if (
		existingUsers.length === 0 ||
		existingBreeds.length === 0 ||
		existingWilayas.length === 0 ||
		existingCommunes.length === 0
	) {
		console.log(
			"❌ Missing reference data. Please run the main seed script first."
		);
		return;
	}

	console.log(`   Users: ${existingUsers.length}`);
	console.log(`   Breeds: ${existingBreeds.length}`);
	console.log(`   Wilayas: ${existingWilayas.length}`);
	console.log(`   Communes: ${existingCommunes.length}\n`);

	// Calculate how many cats to create
	const catsToCreate = targetCount - currentCountResult.value;
	console.log(`🚀 Creating ${catsToCreate} new cats...\n`);

	// Create cats in batches for better performance
	const batchSize = 100;
	const batches = Math.ceil(catsToCreate / batchSize);

	for (let batch = 0; batch < batches; batch++) {
		const batchStart = batch * batchSize;
		const batchEnd = Math.min(batchStart + batchSize, catsToCreate);
		const batchCount = batchEnd - batchStart;

		console.log(
			`   📦 Batch ${batch + 1}/${batches}: Creating ${batchCount} cats...`
		);

		const catsData = [];

		for (let i = 0; i < batchCount; i++) {
			const randomUser =
				existingUsers[Math.floor(Math.random() * existingUsers.length)];
			const randomBreed =
				existingBreeds[
					Math.floor(Math.random() * existingBreeds.length)
				];
			const randomWilaya =
				existingWilayas[
					Math.floor(Math.random() * existingWilayas.length)
				];
			const randomCommune =
				existingCommunes[
					Math.floor(Math.random() * existingCommunes.length)
				];

			const randomName =
				CAT_NAMES[Math.floor(Math.random() * CAT_NAMES.length)];
			const randomDescription =
				CAT_DESCRIPTIONS[
					Math.floor(Math.random() * CAT_DESCRIPTIONS.length)
				];

			// Create some variation in creation dates (spread over last 6 months)
			const now = new Date();
			const sixMonthsAgo = new Date(
				now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000
			);
			const randomDate = new Date(
				sixMonthsAgo.getTime() +
					Math.random() * (now.getTime() - sixMonthsAgo.getTime())
			);

			const catName = `${randomName} ${batchStart + i + 1}`;
			const catSlug = await generateUniqueCatSlug(catName);

			catsData.push({
				name: catName,
				slug: catSlug,
				description: randomDescription,
				age: Math.floor(Math.random() * 15) + 1, // 1-15 years
				gender: Math.random() > 0.5 ? "male" : "female",
				vaccinated: Math.random() > 0.3, // 70% vaccinated
				neutered: Math.random() > 0.4, // 60% neutered
				specialNeeds: Math.random() > 0.8, // 20% special needs
				adopted: Math.random() > 0.7, // 30% adopted
				featured: Math.random() > 0.9, // 10% featured
				status: "available",
				isDraft: Math.random() > 0.9, // 10% drafts
				userId: randomUser.id,
				breedId: randomBreed.id,
				wilayaId: randomWilaya.id,
				communeId: randomCommune.id,
				createdAt: randomDate,
				updatedAt: randomDate,
			});
		}

		// Insert batch
		const startTime = performance.now();
		await db.insert(cats).values(catsData);
		const insertTime = performance.now() - startTime;

		console.log(
			`      ✅ Inserted ${batchCount} cats in ${insertTime.toFixed(2)}ms`
		);
	}

	// Final count check
	const [finalCountResult] = await db.select({ value: count() }).from(cats);

	console.log(`\n✅ Seeding completed!`);
	console.log(`📊 Total cats in database: ${finalCountResult.value}`);
	console.log(
		`🎯 Target reached: ${finalCountResult.value >= targetCount ? "Yes" : "No"}\n`
	);

	return finalCountResult.value;
}

// Run the seeding
const targetCount = parseInt(process.argv[2]) || 1000;

seedLargeDataset(targetCount)
	.then((finalCount) => {
		console.log(
			`🌱 Large dataset seeding completed with ${finalCount} total cats!`
		);
		process.exit(0);
	})
	.catch((error) => {
		console.error("❌ Error seeding large dataset:", error);
		process.exit(1);
	});
